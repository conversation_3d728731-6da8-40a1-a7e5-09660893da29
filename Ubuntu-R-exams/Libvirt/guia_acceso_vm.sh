#!/bin/bash

# Guía paso a paso para acceder a la VM después de apagarla

echo "📋 GUÍA COMPLETA: ACCESO A VM DESPUÉS DE APAGARLA"
echo "================================================="

VM_NAME="Ubuntu-Dev"

echo ""
echo "🔍 PASO 1: VERIFICAR ESTADO ACTUAL"
echo "=================================="

# Configurar libvirt de usuario
export LIBVIRT_DEFAULT_URI="qemu:///session"

echo "Verificando estado de la VM..."
VM_STATUS=$(virsh list --all | grep "$VM_NAME" | awk '{print $3}')

if [ -z "$VM_STATUS" ]; then
    echo "❌ VM '$VM_NAME' no encontrada"
    echo "🔧 Ejecuta: ./restart_vm_after_reboot.sh"
    exit 1
elif [ "$VM_STATUS" = "ejecutando" ]; then
    echo "✅ VM ya está ejecutándose"
    echo "⏭️  Saltando al paso 3 (Conectar)"
elif [ "$VM_STATUS" = "apagado" ]; then
    echo "⚠️  VM está apagada - necesita iniciarse"
else
    echo "ℹ️  Estado de VM: $VM_STATUS"
fi

echo ""
echo "🚀 PASO 2: INICIAR LA VM"
echo "========================"

if [ "$VM_STATUS" != "ejecutando" ]; then
    echo "Iniciando VM '$VM_NAME'..."
    
    if virsh start "$VM_NAME"; then
        echo "✅ VM iniciada correctamente"
        
        # Esperar a que se inicialice
        echo "⏳ Esperando a que la VM se inicialice..."
        sleep 10
        
        # Verificar que está ejecutándose
        if virsh list | grep -q "$VM_NAME"; then
            echo "✅ VM confirmada como ejecutándose"
        else
            echo "❌ Error: VM no aparece como ejecutándose"
            exit 1
        fi
    else
        echo "❌ Error al iniciar la VM"
        echo "🔧 Posibles soluciones:"
        echo "   - Ejecutar: ./restart_vm_after_reboot.sh"
        echo "   - Verificar: virsh list --all"
        exit 1
    fi
else
    echo "✅ VM ya estaba ejecutándose"
fi

echo ""
echo "🌐 PASO 3: CONFIGURAR ACCESO WEB (OPCIONAL)"
echo "==========================================="

# Verificar si el servidor web ya está ejecutándose
if pgrep -f "python3 -m http.server 8080" > /dev/null; then
    echo "✅ Servidor web ya está ejecutándose en puerto 8080"
else
    echo "🚀 Iniciando servidor web para acceso fácil..."
    
    # Verificar si existe el directorio
    if [ -d ~/vm-web-access ]; then
        cd ~/vm-web-access
        python3 -m http.server 8080 &
        WEB_PID=$!
        echo "✅ Servidor web iniciado (PID: $WEB_PID)"
        echo "🌐 Acceso web: http://localhost:8080"
    else
        echo "⚠️  Directorio web no encontrado, ejecutando setup..."
        cd /home/<USER>/Tareas
        ./setup_web_vnc.sh
    fi
fi

echo ""
echo "🖥️  PASO 4: OBTENER INFORMACIÓN DE CONEXIÓN"
echo "==========================================="

# Obtener información de display VNC
DISPLAY_INFO=$(virsh domdisplay "$VM_NAME" 2>/dev/null)

if [ -n "$DISPLAY_INFO" ]; then
    echo "✅ Display VNC disponible: $DISPLAY_INFO"
    
    # Extraer puerto
    if [[ $DISPLAY_INFO =~ vnc://127\.0\.0\.1:([0-9]+) ]]; then
        VNC_DISPLAY=${BASH_REMATCH[1]}
        VNC_PORT=$((5900 + VNC_DISPLAY))
    else
        VNC_PORT=5900
    fi
    
    echo "🔌 Puerto VNC: $VNC_PORT"
    
    # Verificar que el puerto está escuchando
    if ss -tlnp | grep -q ":$VNC_PORT"; then
        echo "✅ Puerto VNC confirmado como activo"
    else
        echo "⚠️  Puerto VNC no detectado, esperando..."
        sleep 5
        if ss -tlnp | grep -q ":$VNC_PORT"; then
            echo "✅ Puerto VNC ahora activo"
        else
            echo "❌ Puerto VNC no disponible"
        fi
    fi
else
    echo "❌ No se pudo obtener información de display"
    DISPLAY_INFO="vnc://127.0.0.1:0"
    VNC_PORT=5900
fi

echo ""
echo "🎯 PASO 5: OPCIONES DE CONEXIÓN"
echo "==============================="

echo ""
echo "📱 OPCIÓN A: ACCESO WEB (MÁS FÁCIL)"
echo "-----------------------------------"
echo "1. Abrir navegador web"
echo "2. Ir a: http://localhost:8080"
echo "3. Seguir las instrucciones en la página"
echo ""

echo "🖥️  OPCIÓN B: CLIENTE VNC NATIVO (RECOMENDADO)"
echo "---------------------------------------------"
echo "1. Instalar cliente VNC (si no está instalado):"
echo "   sudo pacman -S vinagre"
echo ""
echo "2. Conectar usando:"
echo "   vinagre $DISPLAY_INFO"
echo ""
echo "   O manualmente:"
echo "   - Host: 127.0.0.1"
echo "   - Puerto: $VNC_PORT"
echo ""

echo "⚡ OPCIÓN C: COMANDOS RÁPIDOS"
echo "----------------------------"
echo "# Ver estado de VMs:"
echo "virsh list"
echo ""
echo "# Iniciar VM:"
echo "virsh start $VM_NAME"
echo ""
echo "# Conectar con vinagre:"
echo "vinagre \$(virsh domdisplay $VM_NAME)"
echo ""
echo "# Apagar VM:"
echo "virsh shutdown $VM_NAME"
echo ""

echo "⏱️  PASO 6: TIEMPO DE ARRANQUE"
echo "=============================="
echo "Después de conectar, espera:"
echo "• 30-60 segundos para ver el escritorio de Lubuntu"
echo "• Si ves pantalla negra, espera un poco más"
echo "• El primer arranque puede tardar más"
echo ""

echo "🎉 RESUMEN RÁPIDO"
echo "================="
echo "1. ✅ VM iniciada: $VM_NAME"
echo "2. 🌐 Acceso web: http://localhost:8080"
echo "3. 🖥️  VNC directo: $DISPLAY_INFO"
echo "4. ⏳ Esperar 30-60 segundos después de conectar"
echo ""
echo "💡 PARA LA PRÓXIMA VEZ:"
echo "Simplemente ejecuta: ./guia_acceso_vm.sh"
